<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>课消记录查询</span>
        </div>
      </template>

      <!-- 锁定信息提示 -->
      <el-alert
        v-if="lockedInfo"
        :title="lockedInfo"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学生手机" prop="studentPhone">
          <el-input
            v-model="queryParams.studentPhone"
            placeholder="请输入学生手机号"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="queryParams.subject"
            placeholder="请选择学科"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="specification">
          <el-select
            v-model="queryParams.specification"
            placeholder="请选择课型"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="老师" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="请输入老师姓名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="课消时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="有效" value="active" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="consumptionList" border>
        <el-table-column label="学生姓名" prop="studentName" width="100" />
        <el-table-column label="学生手机" prop="studentPhone" width="120" />
        <el-table-column label="学科" prop="subject" width="80" />
        <el-table-column label="课型" prop="specification" width="100" />
        <el-table-column label="性质" prop="nature" width="80" />
        <el-table-column
          label="消费课时"
          prop="consumedHours"
          width="100"
          align="right"
        />
        <el-table-column label="课时批次" prop="batchNo" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.batchNo">{{ scope.row.batchNo }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="老师" prop="teacherName" width="100" />
        <el-table-column label="课程ID" prop="courseId" width="120" />
        <el-table-column label="课消时间" prop="consumptionTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.consumptionTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === "active" ? "有效" : "已取消" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          prop="remark"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="CourseConsumptionQuery">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { parseTime } from "@/utils/ruoyi";
import { listConsumptionRecords } from "@/api/management/studentCourseHours";
import { useRoute } from 'vue-router';

// 接收props参数（对话框模式）
const props = defineProps({
  studentId: {
    type: [String, Number],
    default: null
  },
  courseHoursId: {
    type: String,
    default: ''
  },
  studentName: {
    type: String,
    default: ''
  },
  studentPhone: {
    type: String,
    default: ''
  },
  subject: {
    type: String,
    default: ''
  },
  specification: {
    type: String,
    default: ''
  },
  isDialogMode: {
    type: Boolean,
    default: false
  }
});

// 获取路由参数（独立页面模式）
const route = useRoute();
const routeParams = {
  studentId: route.query.studentId || null,
  courseHoursId: route.query.courseHoursId || null,
  studentName: route.query.studentName || '',
  studentPhone: route.query.studentPhone || '',
  subject: route.query.subject || '',
  specification: route.query.specification || ''
};

// 根据模式选择参数来源
const effectiveParams = computed(() => {
  if (props.isDialogMode) {
    return {
      studentId: props.studentId,
      courseHoursId: props.courseHoursId,
      studentName: props.studentName,
      studentPhone: props.studentPhone,
      subject: props.subject,
      specification: props.specification
    };
  }
  return routeParams;
});

// 响应式数据
const loading = ref(true);
const consumptionList = ref([]);
const total = ref(0);
const dateRange = ref([]);

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  studentId: null,
  courseHoursId: "",
  studentName: "",
  studentPhone: "",
  subject: "",
  specification: "",
  teacherName: "",
  status: "",
  startTime: "",
  endTime: "",
});

// 计算属性
const isStudentLocked = computed(() => !!effectiveParams.value.studentId);
const isCourseLocked = computed(() => !!(effectiveParams.value.subject && effectiveParams.value.specification));
const isCourseHoursLocked = computed(() => !!effectiveParams.value.courseHoursId);

const lockedInfo = computed(() => {
  if (isCourseHoursLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}，课程 ${effectiveParams.value.subject} - ${effectiveParams.value.specification}，课时记录 ${effectiveParams.value.courseHoursId}`;
  } else if (isCourseLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}，课程 ${effectiveParams.value.subject} - ${effectiveParams.value.specification}`;
  } else if (isStudentLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}`;
  }
  return null;
});

// 初始化查询参数
const initQueryParams = () => {
  queryParams.value.studentId = effectiveParams.value.studentId;
  queryParams.value.courseHoursId = effectiveParams.value.courseHoursId;
  queryParams.value.studentName = effectiveParams.value.studentName;
  queryParams.value.studentPhone = effectiveParams.value.studentPhone;
  queryParams.value.subject = effectiveParams.value.subject;
  queryParams.value.specification = effectiveParams.value.specification;
};

// 查询表单引用
const queryRef = ref();

// 获取列表数据
const getList = async () => {
  loading.value = true;

  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.startTime = "";
    queryParams.value.endTime = "";
  }

  try {
    const response = await listConsumptionRecords(queryParams.value);
    consumptionList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error("获取课消记录失败:", error);
    ElMessage.error("获取课消记录失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields();
  dateRange.value = [];

  // 重置时保持锁定的字段
  initQueryParams();

  handleQuery();
};

// 监听有效参数变化，重新初始化查询参数并刷新数据
watch(() => effectiveParams.value, (newParams, oldParams) => {
  // 检查关键参数是否发生变化
  const keyChanged =
    newParams.studentId !== oldParams?.studentId ||
    newParams.courseHoursId !== oldParams?.courseHoursId ||
    newParams.subject !== oldParams?.subject ||
    newParams.specification !== oldParams?.specification;

  if (keyChanged) {
    initQueryParams();
    getList();
  }
}, { deep: true, immediate: false });

// 初始化
onMounted(() => {
  initQueryParams();
  getList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}
</style>
