import request from '@/utils/request'

// 学生课时查询参数
export interface StudentCourseHoursQuery {
  pageNum?: number
  pageSize?: number
  studentName?: string
  studentPhone?: string
  subject?: string
  specification?: string
  nature?: string
  status?: string
  teacherName?: string
  teacherPhone?: string
}

// 课时调整请求
export interface AdjustRequest {
  courseHoursId: string
  studentId: number
  subject: string
  specification: string
  purchasedHoursAdjustment: number
  giftHoursAdjustment: number
  unitPrice: number
  adjustmentReason: string
}

// 新增课时请求
export interface AddCourseHoursRequest {
  studentId: string
  subject: string
  specification: string
  nature: string
  purchasedHours: number
  giftHours: number
  unitPrice: number
  reason: string
}

// 更新课时请求
export interface UpdateCourseHoursRequest {
  courseHoursId: string
  studentId: string
  subject: string
  specification: string
  purchasedHours: number
  remainingPurchasedHours: number
  giftHours: number
  remainingGiftHours: number
  unitPrice: number
  adjustmentReason: string
}

// 录入课消请求
export interface RecordConsumptionRequest {
  courseHoursId: string
  studentId: string
  subject: string
  specification: string
  nature: string
  consumedHours: number
  teacherId?: string
  courseId?: string
  remark: string
}

// 课消记录查询参数
export interface ConsumptionQuery {
  pageNum?: number
  pageSize?: number
  studentId?: number
  courseHoursId?: string
  studentName?: string
  studentPhone?: string
  subject?: string
  specification?: string
  teacherName?: string
  startTime?: string
  endTime?: string
  status?: string
}

// 调整历史查询参数
export interface AdjustmentHistoryQuery {
  pageNum?: number
  pageSize?: number
  courseHoursId?: string
  studentId?: string
  studentName?: string
  studentPhone?: string
  subject?: string
  specification?: string
  operatorName?: string
  startTime?: string
  endTime?: string
}

/**
 * 查询学生课时记录列表
 */
export function listStudentCourseHours(query: StudentCourseHoursQuery) {
  return request({
    url: '/management/student-course-hours/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取学生课时详情（用于调整时获取最新数据）
 */
export function getStudentCourseHoursDetail(studentId: number, subject: string, specification: string) {
  return request({
    url: '/management/student-course-hours/detail',
    method: 'get',
    params: {
      studentId,
      subject,
      specification
    }
  })
}

/**
 * 根据课时包ID获取课时包详情
 */
export function getCourseHoursById(courseHoursId: string) {
  return request({
    url: `/management/student-course-hours/${courseHoursId}`,
    method: 'get'
  })
}

/**
 * 手动调整学生课时
 */
export function adjustStudentCourseHours(data: AdjustRequest) {
  return request({
    url: '/management/student-course-hours/adjust',
    method: 'post',
    data: data
  })
}

/**
 * 直接设置学生课时值
 */
export function updateStudentCourseHours(data: UpdateCourseHoursRequest) {
  return request({
    url: '/management/student-course-hours/update',
    method: 'post',
    data: data
  })
}

/**
 * 录入课消
 */
export function recordCourseConsumption(data: RecordConsumptionRequest) {
  return request({
    url: '/management/student-course-hours/consume',
    method: 'post',
    data: data
  })
}

/**
 * 新增课时
 */
export function addCourseHours(data: AddCourseHoursRequest) {
  return request({
    url: '/management/student-course-hours/add',
    method: 'post',
    data: data
  })
}

/**
 * 查询课消记录列表
 */
export function listConsumptionRecords(query: ConsumptionQuery) {
  return request({
    url: '/management/student-course-hours/consumption/list',
    method: 'get',
    params: query
  })
}

// 调整历史记录
export interface AdjustmentHistoryRecord {
  id: string
  studentId: number
  studentName: string
  studentPhone: string
  subject: string
  specification: string
  nature: string
  adjustmentType: string
  adjustmentHours: number
  purchasedHoursAdjustment: number
  giftHoursAdjustment: number
  beforeTotalHours: number
  afterTotalHours: number
  beforeRemainingHours: number
  afterRemainingHours: number
  beforePurchasedHours: number
  afterPurchasedHours: number
  beforeGiftHours: number
  afterGiftHours: number
  adjustmentReason: string
  operatorId: number
  operatorName: string
  adjustmentTime: string
  createTime: string
}

/**
 * 查询调整历史记录列表
 */
export function listAdjustmentHistory(query: AdjustmentHistoryQuery) {
  return request({
    url: '/management/student-course-hours/adjustment/history',
    method: 'get',
    params: query
  })
}

// 课时导入相关接口
export interface CourseHoursImportResult {
  totalRows: number
  successRows: number
  failedRows: number
  createdTeachers: number
  createdStudents: number
  createdRelations: number
  createdHours: number
  errorMessage?: string
  detailMessage?: string
}

/**
 * 导入课时数据
 */
export function importCourseHours(file: File, overrideExisting: boolean = false): Promise<any> {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('overrideExisting', String(overrideExisting))

  return request({
    url: '/management/course-hours/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 课时导入可能需要较长时间，设置5分钟超时
    timeout: 300000
  })
}

/**
 * 下载导入模板
 */
export function downloadTemplate(): Promise<any> {
  return request({
    url: '/management/course-hours/template',
    method: 'post',
    responseType: 'blob'
  })
}

/**
 * 导出学生课时数据
 */
export function exportStudentCourseHours(query: StudentCourseHoursQuery): Promise<any> {
  return request({
    url: '/management/student-course-hours/export',
    method: 'post',
    data: query,
    responseType: 'blob',
    // 设置3分钟超时时间
    timeout: 180000
  })
}
