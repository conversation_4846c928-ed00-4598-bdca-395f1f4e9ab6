import request from '@/utils/request';
import { number } from 'echarts';
import {ElMessage} from "element-plus";
// 用户存储当前阶段提交信息的sessionStorage的key
// key的格式为：STEP_SUBMIT_{courseId}_{sectionId}_{wordId}_{stepId}
export const WORD_STEP_SUBMIT_PREFIX_sessionStorage_KEY = "WORD_STEP_SUBMIT_"
// 定义上课所需要的一些必要数据，如课程id，学生id，老师id等
export declare interface CourseRequiredInfo {
    courseId: string
    studentId: string
    teacherId: string
}

// 课程信息接口
export declare interface CourseInfo {
    id: string
    type: string
    status: string
    teacher: {
        id: string
        name: string
        avatar: string
    }
    student: {
        id: string
        name: string
        avatar: string
    }
    scheduledStartTime: string
    scheduledEndTime: string
    actualStartTime: string
    actualEndTime: string
    durationMinutes: number
    content: CourseContent
}

// 课程内容接口
export declare interface CourseContent {
    currentSectionIndex: number
    sections: Section[]
}

// 课程章节接口
export declare interface Section {
    id: string
    title: string
    type: string
    status: string
    currentWordIndex: number
    startTime: string
    endTime: string
    words: Word[]
}

// 单词接口
export declare interface Word {
    id: string
    status: string
    result: string
    currentStepIndex: number
    wordInfo: WordInfo
    steps: Step[]
    marked?: boolean
}

// 单词信息接口
export declare interface WordInfo {
    word: string
    syllables: string
    phoneticUk: string
    phoneticUs: string
    difficulty: string
    audioUkUrl: string
    audioUsUrl: string
    videoUrl: string
    meanings: Meanings
    sentences: Sentence[]
}

//上新课接口
export declare interface NewCourse {
    textbookId: string;
    unitId: string;
    wordIds: string[];
    textbookItemIds: string[];
}

export declare interface Meanings {
    pos: Pos[]
    practices: string[]
}

// 单词释义接口
export declare interface Pos {
    pos: string
    def: string
}

// 例句接口
export declare interface Sentence {
    sentenceEn: string
    sentenceCn: string
    audioUkUrl: string
    audioUsUrl: string
}

// 学习步骤接口
export declare interface Step {
    id: string
    type: string
    status: string
    result: string
    startTime: string
    endTime: string
    sentenceOrder: string[]
    options: Option[]
    studentAnswer: string
    answer: string
}

// 选项接口
export declare interface Option {
    text: string
    isCorrect: boolean
}

//抗遗忘复习接口
export declare interface ReviewCourse {
    studentId: string;
    scheduledTime: string;
    detailContents: DetailContent[];
}

declare interface DetailContent {
    id: string;
    courseId: string;
    reviewType: string;
    name: string;
    actualTime: string;
    status: string;
    wordCount: number;
}

export declare interface WordTestInfo {
    testedTime:string;
    testedWordNum:number;
    successWordNum:number;
    successRate:string;
    estimatedWordNum:number;
    result:string;
    status:string;
    consumTime:string;
    suggestions:string;
    /**
     * 测验详细信息
     */
    testDetailInfo: TestDetailInfo;
    /**
     * 错词讲义PDF下载地址
     */
    errorHandoutPdfUrl?: string;
    /**
     * 错题练习PDF下载地址
     */
    errorExercisePdfUrl?: string;
}

export declare interface TestDetailInfo {
    /**
     * 上学期单词正确率
     */
    lastSemesterWordCollectRate: string;
    /**
     * 
     */
    lastSemesterSuggestions: string;
    /**
     * 高分词汇正确率
     */
    specialWordCollectRate: string;
    /**
     * 高分词汇学习建议
     */
    specialWordSuggestions: string;
    /**
     * 建议教材
     */
    suggestTextbookName: string;
    /**
     * 建议学习计划
     */
    suggestLearnTime: string;
    /**
     * 高分词汇学习计划
     */
    specialWordLearnSuggestions: string;
}

/**
 * 获取课程信息
 * @param courseId 课程ID
 * @returns 课程信息
 */
export async function fetchCourseInfoApi(courseId: string) {
    return request({
        url: `/course/info/${courseId}`,
        method: 'get'
    });
}
export const COURSE_sessionStorage_INFO_KEY = 'COURSE_sessionStorage_INFO_';
/**
 * 开始上课
 * @param courseId
 */
export function startCourseApi(courseId: string) {
    return request({
        url: `/course/start/${courseId}`,
        method: 'post',
        data: courseId,
    });
}

/**
 * 下课接口
 * @param courseId
 */
export function endCourseApi(courseId: string) {
    return request({
        url: `/course/end/${courseId}`,
        method: 'post',
        data: courseId,
    });
}

/**
 * 上新课
 * @param courseId
 * @param data
 */
export function startCourseLearningApi(courseId: string, data: NewCourse) {
    return request({
        url: `/course/start-learning/${courseId}`,
        method: 'post',
        data: data,
    });
}

/**
 * 生成讲义pdf
 * @param courseId
 * @param data
 */
export function generatePdfApi(courseId: string, textbookItemIds: string[]) {
    return request({
        url: `/course/download-pdf/${courseId}`,
        method: 'post',
        data: textbookItemIds,
    });
}

export declare interface SubmitCourseStepApiParam {
    wordId: string;
    params: SubmitCourseStepApiReq[];
}
export declare interface SubmitCourseStepApiReq {
    stepId: string;
    result: string;
    studentAnswer: string;
}


export function getWordStepResultByWordId(courseId:string, sectionId: string, wordId: string, courseInfo?: CourseInfo): SubmitCourseStepApiParam {
    // debugger
    let result = sessionStorage.getItem(WORD_STEP_SUBMIT_PREFIX_sessionStorage_KEY + courseId + "_" + sectionId + "_" + wordId)
    if (result != null) {
        return JSON.parse(result)
    }
    // 如果sessionStorage没有，就从接口获取
    if (courseInfo != null) {
        let currentSection = getCurrentSection(courseInfo)
        // 从courseInfo中获取出来words里的word的id与wordId一致的，把steps遍历出来，组装成SubmitCourseStepApiParam对象
        let word = currentSection?.words?.find(item => item?.id === wordId)
        if (word != null) {
            let reqArr = word.steps?.map((step) => { return { stepId: step.id, result: step.result, studentAnswer: step.studentAnswer } })
            return { wordId: wordId, params: reqArr }
        }
    }

    return null;
}

/**
 * 课程学习步骤结果提交
 * @param courseId
 * @param wordId
 * @param req
 * @param upload 是否将结果上传到云端
 */
export function submitCourseStepApi(courseId: string, sectionId: string, wordId: string, req: SubmitCourseStepApiReq, upload: boolean = false) {
    // debugger
    // 先存储一份在本地
    let submitCourseStepApiParam = JSON.parse(sessionStorage.getItem(WORD_STEP_SUBMIT_PREFIX_sessionStorage_KEY + courseId + "_" + sectionId + "_" + wordId))
        || { wordId: wordId, params: [] }
    // 判断当前的step是否已经push过了
    if (!submitCourseStepApiParam?.params?.some(item => item.stepId === req.stepId)) {
        submitCourseStepApiParam.params.push({ stepId: req.stepId, result: req.result, studentAnswer: req.studentAnswer })
        sessionStorage.setItem(WORD_STEP_SUBMIT_PREFIX_sessionStorage_KEY + courseId + "_" + sectionId + "_" + wordId, JSON.stringify(submitCourseStepApiParam))
    }
    if (upload) {
        let stepId = req.stepId
        return request({
            url: `/course/step/submit/${stepId}`,
            method: 'post',
            data: req
        });

    }

}
/**
 * 获取抗遗忘复习列表
 * @param studentId 
 * @returns 
 */
export function fetchReviewCourseInfoApi(studentId: string) {
    return request({
        url: `/course/review/list/${studentId}`,
        method: 'get'
    });
}
/**
 * 开始抗遗忘复习
 * @param id 
 * @param reviewId 
 * @returns 
 */
export function startReviewCourseInfoApi(id: string, reviewId: string) {
    return request({
        url: `/course/start-review/${id}/review/${reviewId}`,
        method: 'post'
    });
}

/**
 * 下课前复习
 * @param courseId 
 * @returns 
 */
export function startEndReviewApi(courseId: string) {
    return request({
        url: `/course/start-end-review/${courseId}`,
        method: 'post'
    });
}

/**
 * 开始词汇测试
 * @param studentId 
 * @param courseId 
 * @returns 
 */
export function startWordTestCourseInfoApi(studentId: string,courseId: string,data:any) {
    return request({
        url: `/course/start-test/${studentId}/${courseId}`,
        method: 'post',
        data:data
    });
}

/**
 * 获取词汇测试列表
 * @param studentId
 * @param courseId
 */
export function getWordTestListApi(studentId: string) {
    return request({
        url: `/course/word/test/list/${studentId}`,
        method: 'get'
    });
}

/**
 * 下载词汇测试错误内容
 * @param courseId 课程ID
 * @param type 下载类型 (error_handout: 错词讲义, error_exercise: 错题练习)
 * @returns 下载信息
 */
export function downloadWordTestErrorMaterialApi(courseId: string, type: 'error_handout' | 'error_exercise') {
    return request({
        url: `/course/word-test/error-material/download`,
        method: 'get',
        params: { courseId, type }
    });
}

/**
 * 获取词汇测试列表
 * @param studentId
 * @param courseId
 */
export function getWordByCourseIdApi(courseId: string) {
    return request({
        url: `/course/word/test/${courseId}`,
        method: 'get'
    });
}


/**
 * 环节结束
 * @param sectionId
 */
export function endSectionApi(sectionId: string) {
    return request({
        url: `/course/end-section/${sectionId}`,
        method: 'post'
    });
}




/**
 * 获取当前的环节信息
 * @param courseInfo
 */
export function getCurrentSection(courseInfo: CourseInfo): Section | null {
    if (!courseInfo.content) {
        return null
    }
    const index = courseInfo.content.currentSectionIndex;
    if (index >= 0 && index < courseInfo.content.sections.length) {
        return courseInfo.content.sections[index];
    }
    return null
}

/**
 * 获取当前的单词信息
 * @param courseInfo
 */
export function getCurrentWordInfo(courseInfo: CourseInfo): WordInfo | null {
    let currentSection = getCurrentSection(courseInfo)
    if (currentSection == null) {
        return null
    }
    return currentSection.words[currentSection.currentWordIndex]?.wordInfo
}

/**
 * 根据单词名称获取课程里面对应的课程信息
 * @param courseInfo
 * @param wordText
 */
export async function getWordInfoByWord(courseInfo: CourseInfo, wordText: string): Promise<Word | null> {
    let currentSection = getCurrentSection(courseInfo)
    if (currentSection == null) {
        return null
    }
    let wordsArr = currentSection?.words
    return wordsArr.find(word => word?.wordInfo?.word === wordText)
}

/**
 * 获取当前的步骤阶段
 * @param courseInfo
 */
export function getCurrentStepInfo(courseInfo: CourseInfo): Step | null {
    let currentSection = getCurrentSection(courseInfo)
    if (currentSection == null) {
        return null
    }
    let steps = currentSection.words[currentSection.currentWordIndex]?.steps
    let currentStepIndex = currentSection.words[currentSection.currentWordIndex]?.currentStepIndex
    return steps[currentStepIndex]
}


export declare interface CurrentStepInfo {
    sectionId: string
    wordId: string
    wordInfo: WordInfo,
    step: Step
}

/**
 * 根据当前步骤索引，获取整个单词信息
 * @param courseInfo
 * @param stepIndex
 * @param wordText
 */
export async function getStepInfoByIndex(courseInfo: CourseInfo, stepIndex: number, wordText?: string): Promise<CurrentStepInfo | null> {
    let currentSection = getCurrentSection(courseInfo)
    if (currentSection == null) {
        ElMessage.warning("当前课程环节信息获取失败");
        return null
    }
    // 有给到单词信息，则直接根据当前单词获取
    if (wordText != null && wordText != '') {
        let word = getWordInfoByWord(courseInfo, wordText)
        let wordInfo = (await word).wordInfo
        let steps = (await word).steps
        let step = steps[stepIndex]
        let wordId = wordInfo?.id
        return { sectionId: currentSection.id, wordId: wordId, wordInfo: wordInfo, step: step }
    }
    let wordInfo = getCurrentWordInfo(courseInfo)
    let steps = currentSection.words[currentSection.currentWordIndex]?.steps
    let step = steps[stepIndex]
    let wordId = currentSection.words[currentSection.currentWordIndex]?.id
    return { sectionId: currentSection.id, wordId: wordId, wordInfo: wordInfo, step: step }
}

/**
 * 根据type类型获取step信息
 * @param courseInfo
 * @param stepType
 * @param wordText
 */
export async function getStepInfoByType(courseInfo: CourseInfo, stepType: string, wordText?: string): Promise<CurrentStepInfo | null> {
    let currentSection = getCurrentSection(courseInfo)
    // debugger
    if (currentSection == null) {
        return null
    }
    // 有给到单词信息，则直接根据当前单词获取
    if (wordText != null && wordText != '') {
        let word = getWordInfoByWord(courseInfo, wordText)
        let wordInfo = (await word).wordInfo
        let steps = (await word).steps
        let step = steps.find((item) => item.type === stepType)
        let wordId = wordInfo?.id
        return { sectionId: currentSection.id, wordId: wordId, wordInfo: wordInfo, step: step }
    }
    let wordInfo = getCurrentWordInfo(courseInfo)
    let steps = currentSection.words[currentSection.currentWordIndex]?.steps
    let step = steps.find((item) => item.type === stepType)
    let wordId = currentSection.words[currentSection.currentWordIndex]?.id
    return { sectionId: currentSection.id, wordId: wordId, wordInfo: wordInfo, step: step }
}