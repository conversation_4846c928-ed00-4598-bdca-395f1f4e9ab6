<template>
  <div class="curriculum-page" :class="{ 'dialog-mode': isDialogMode }">
    <!-- 页面头部 -->
    <!-- <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon class="title-icon"><Calendar /></el-icon>
          课表管理
        </h1>
        <div class="header-meta">
          <span class="current-time">{{ currentTime }}</span>
          <el-divider direction="vertical" />
          <span class="user-info">{{ userRole }} - {{ userName }}</span>
        </div>
      </div>
    </div> -->

    <!-- 主内容区 -->
    <div class="page-content">
      <!-- 左侧课表区域 -->
      <div class="schedule-section">
        <el-card class="schedule-card" shadow="never">
          <!-- 课表工具栏 -->
          <template #header>
            <div class="schedule-toolbar">
              <div class="view-controls">
                <el-radio-group v-model="currentView" @change="handleViewChange">
                  <el-radio-button label="week">
                    <el-icon><Grid /></el-icon>
                    <span>周视图</span>
                  </el-radio-button>
                  <el-radio-button label="month">
                    <el-icon><Calendar /></el-icon>
                    <span>月视图</span>
                  </el-radio-button>
                </el-radio-group>
              </div>

              <div class="schedule-info">
                <!-- 显示当前查看的用户信息 -->
                <el-tag v-if="isDialogMode && externalTeacherContext?.name" type="primary" size="small">
                  教师：{{ externalTeacherContext.name }}
                </el-tag>
                <el-tag v-else-if="isDialogMode && externalStudentContext?.name" type="success" size="small">
                  学生：{{ externalStudentContext.name }}
                </el-tag>
                <el-tag v-else-if="route.query.teacherName" type="primary" size="small">
                  教师：{{ route.query.teacherName }}
                </el-tag>
                <el-tag v-else-if="route.query.studentName" type="success" size="small">
                  学生：{{ route.query.studentName }}
                </el-tag>

                <el-button
                :icon="Plus"
                @click="showScheduleDialog = true"
                v-if="canCreateSchedule"
                circle
                title="排课"
                >
                </el-button>
                <el-button
                :icon="Plus"
                @click="handleAddCourseClick"
                v-if="canCreateSchedule"
                circle
                type="success"
                title="添加课程"
                >
                </el-button>
                <el-button :icon="Refresh" @click="refreshData" :loading="loading" circle />
                <!-- <el-tag type="info" size="small"> 共 {{ totalCourses }} 节课 </el-tag>
                <el-tag type="warning" size="small" v-if="todayCourses > 0">
                  今日 {{ todayCourses }} 节
                </el-tag> -->
              </div>
            </div>
          </template>

          <!-- 课表内容 -->
          <div class="schedule-content" v-loading="loading">
            <!-- 周视图 -->
            <WeekView
              v-if="currentView === 'week'"
              :courses="currentSchedules"
              :show-student-name="showStudentName"
              :show-teacher-name="showTeacherName"
              :show-actions="showCourseActions"
              :teacher-id="currentTeacherId"
              :student-id="currentStudentId"
              @week-change="handleDateChange"
              @slot-click="handleSlotClick"
              @course-action="handleCourseAction"
            />

            <!-- 月视图 -->
            <MonthView
              v-else-if="currentView === 'month'"
              :courses="currentSchedules"
              :show-student-name="showStudentName"
              :show-teacher-name="showTeacherName"
              :show-actions="showCourseActions"
              :teacher-id="currentTeacherId"
              :student-id="currentStudentId"
              @month-change="handleDateChange"
              @date-click="handleDateClick"
              @course-action="handleCourseAction"
            />

            <!-- 空状态 -->
            <div v-if="!loading && currentSchedules.length === 0" class="empty-schedule">
              <el-empty description="暂无课程安排" :image-size="120">
                <template #image>
                  <el-icon class="empty-icon"><Calendar /></el-icon>
                </template>
                <el-button
                  type="primary"
                  @click="showScheduleDialog = true"
                  v-if="canCreateSchedule"
                >
                  立即排课
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧复习课区域 -->
      <div class="review-section">
        <ReviewPanel
          :teacher-id="currentTeacherId"
          :student-id="currentStudentId"
          @review-start="handleReviewStart"
          @review-complete="handleReviewComplete"
          @review-view="handleReviewView"
        />
      </div>
    </div>

    <!-- 排课弹窗 - 只有在需要时才创建 -->
    <ScheduleDialog
      v-if="showScheduleDialog"
      v-model="showScheduleDialog"
      :teacher-id="currentTeacherId"
      @success="handleScheduleSuccess"
    />

    <!-- 复习课弹窗 - 只有在需要时才创建 -->
    <ReviewScheduleDialog
      v-if="showReviewScheduleDialog"
      v-model="showReviewScheduleDialog"
      :teacher-id="currentTeacherId"
      @success="handleReviewScheduleSuccess"
      :key="`review-dialog-${currentTeacherId}`"
    />

    <!-- 消课弹窗 - 只有在需要时才创建 -->
    <ConsumeCourseDialog
      v-if="showConsumeCourseDialog"
      v-model="showConsumeCourseDialog"
      :course="selectedCourse"
      @success="handleConsumeCourseSuccess"
    />

    <!-- 快捷操作悬浮按钮 -->
    <div class="floating-actions" v-if="isMobile">
      <el-button
        type="primary"
        :icon="Plus"
        @click="showScheduleDialog = true"
        circle
        size="large"
        class="floating-btn schedule-btn"
        v-if="canCreateSchedule"
        title="排课"
      />
      <el-button
        v-show="false"
        type="success"
        :icon="Plus"
        @click="handleAddCourseClick"
        circle
        size="large"
        class="floating-btn review-btn"
        v-if="canCreateSchedule"
        title="添加课程"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { Calendar, Grid, Plus, Refresh } from "@element-plus/icons-vue";
import { useCurriculumStore } from "@/stores/curriculum";
import useUserStore from "@/store/modules/user";
import WeekView from "./components/WeekView.vue";
import MonthView from "./components/MonthView.vue";
import ReviewPanel from "./components/ReviewPanel.vue";
import ScheduleDialog from "./components/ScheduleDialog.vue";
import ReviewScheduleDialog from "./components/ReviewScheduleDialog.vue";
import ConsumeCourseDialog from "./components/ConsumeCourseDialog.vue";

// Props - 支持作为弹窗组件使用
const props = defineProps({
  // 是否为弹窗模式
  isDialogMode: {
    type: Boolean,
    default: false
  },
  // 外部传入的教师ID（弹窗模式下使用）
  teacherId: {
    type: [String, Number],
    default: null
  },
  // 外部传入的学生ID（弹窗模式下使用）
  studentId: {
    type: [String, Number],
    default: null
  },
  // 外部传入的教师上下文信息（弹窗模式下使用）
  externalTeacherContext: {
    type: Object,
    default: () => ({})
  },
  // 外部传入的学生上下文信息（弹窗模式下使用）
  externalStudentContext: {
    type: Object,
    default: () => ({})
  }
});

const router = useRouter();
const route = useRoute();
const curriculumStore = useCurriculumStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const showScheduleDialog = ref(false);
const showReviewScheduleDialog = ref(false);
const showConsumeCourseDialog = ref(false);
const selectedCourse = ref(null);
const currentTimeInterval = ref(null);
const currentTimeText = ref("");

// 从用户store获取当前用户信息
const currentUser = computed(() => ({
  id: userStore.id,
  name: userStore.name,
  role: "teacher", // 恢复正确的角色判断，实际应该从userStore.roles判断
}));

// 计算属性
const currentView = computed({
  get: () => curriculumStore.currentView,
  set: (value) => curriculumStore.setCurrentView(value),
});

const currentSchedules = computed(() => {
  if (currentView.value === "week") {
    return curriculumStore.currentWeekSchedules;
  } else {
    return curriculumStore.currentMonthSchedules;
  }
});

const totalCourses = computed(() => {
  console.log("=== totalCourses computed DEBUG ===");
  console.log(
    "curriculumStore.schedules:",
    curriculumStore.schedules,
    "type:",
    typeof curriculumStore.schedules,
    "isArray:",
    Array.isArray(curriculumStore.schedules)
  );

  if (!Array.isArray(curriculumStore.schedules)) {
    console.error(
      "curriculumStore.schedules is not an array:",
      curriculumStore.schedules
    );
    return 0;
  }

  return curriculumStore.schedules.length;
});

const todayCourses = computed(() => {
  console.log("=== todayCourses computed DEBUG ===");
  console.log(
    "curriculumStore.schedules:",
    curriculumStore.schedules,
    "type:",
    typeof curriculumStore.schedules,
    "isArray:",
    Array.isArray(curriculumStore.schedules)
  );

  if (!Array.isArray(curriculumStore.schedules)) {
    console.error(
      "curriculumStore.schedules is not an array:",
      curriculumStore.schedules
    );
    return 0;
  }

  const today = new Date().toDateString();
  return curriculumStore.schedules.filter((course) => {
    return new Date(course.startTime).toDateString() === today;
  }).length;
});

const currentTime = computed(() => currentTimeText.value);

const userName = computed(() => currentUser.value.name);

const userRole = computed(() => {
  const roleMap = {
    teacher: "老师",
    student: "学生",
    admin: "管理员",
  };
  return roleMap[currentUser.value.role] || "用户";
});

// 权限控制
const canCreateSchedule = computed(() => {
  // 弹窗模式下，如果是查看学生课表，则不显示排课和加课按钮
  if (props.isDialogMode && props.studentId) {
    return false;
  }
  return true; // 暂时允许所有用户创建课表
  // return ["teacher", "admin"].includes(currentUser.value.role);
});

const showStudentName = computed(() => {
  return currentUser.value.role !== "student";
});

const showTeacherName = computed(() => {
  return currentUser.value.role !== "teacher";
});

// 是否显示课程操作按钮（停课、调课等）
const showCourseActions = computed(() => {
  // 弹窗模式下，如果是查看学生课表，则不显示操作按钮
  if (props.isDialogMode && props.studentId) {
    return false;
  }
  // 弹窗模式下，如果是查看教师课表，则显示操作按钮
  if (props.isDialogMode && props.teacherId) {
    return true;
  }
  // 非弹窗模式下，根据用户角色判断
  return currentUser.value.role === "teacher";
});

const currentTeacherId = computed(() => {
  // 弹窗模式下优先使用外部传入的teacherId
  if (props.isDialogMode && props.teacherId) {
    return String(props.teacherId);
  }
  // 优先使用路由参数中的teacherId
  if (route.query.teacherId) {
    return String(route.query.teacherId);
  }
  // 如果当前用户是教师，使用当前用户ID
  if (currentUser.value.role === "teacher") {
    return String(currentUser.value.id);
  }
  return '';
});

const currentStudentId = computed(() => {
  // 弹窗模式下优先使用外部传入的studentId
  if (props.isDialogMode && props.studentId) {
    return String(props.studentId);
  }
  // 优先使用路由参数中的studentId
  if (route.query.studentId) {
    return String(route.query.studentId);
  }
  // 如果当前用户是学生，使用当前用户ID
  if (currentUser.value.role === "student") {
    return String(currentUser.value.id);
  }
  return '';
});

const isMobile = computed(() => {
  return window.innerWidth <= 768;
});

// 方法
const updateCurrentTime = () => {
  currentTimeText.value = new Date().toLocaleString("zh-CN", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const startTimeUpdate = () => {
  updateCurrentTime();
  currentTimeInterval.value = setInterval(updateCurrentTime, 60000); // 每分钟更新
};

const stopTimeUpdate = () => {
  if (currentTimeInterval.value) {
    clearInterval(currentTimeInterval.value);
    currentTimeInterval.value = null;
  }
};

const fetchScheduleData = async () => {
  loading.value = true;
  try {
    const params = {
      viewType: currentView.value,
      startDate: formatDate(getViewStartDate()),
      endDate: formatDate(getViewEndDate()),
      type: 'all',
    };

    // 弹窗模式下优先使用外部传入的参数
    if (props.isDialogMode) {
      if (props.teacherId) {
        params.teacherId = String(props.teacherId);
      } else if (props.studentId) {
        params.studentId = String(props.studentId);
      }
    } else {
      // 非弹窗模式下使用路由参数或当前用户信息
      if (route.query.teacherId) {
        params.teacherId = String(route.query.teacherId);
      } else if (route.query.studentId) {
        params.studentId = String(route.query.studentId);
      } else {
        // 根据当前用户角色添加筛选条件
        if (currentUser.value.role === "teacher") {
          params.teacherId = String(currentUser.value.id);
        } else if (currentUser.value.role === "student") {
          params.studentId = String(currentUser.value.id);
        }
      }
    }

    console.log("📅 fetchScheduleData 调用参数:", params);
    console.log("📅 路由参数:", route.query);
    console.log("📅 弹窗模式:", props.isDialogMode);
    console.log("📅 外部教师ID:", props.teacherId);

    await curriculumStore.fetchSchedules(params);
  } finally {
    loading.value = false;
  }
};

const getViewStartDate = () => {
  if (currentView.value === "week") {
    return getStartOfWeek(curriculumStore.selectedDate);
  } else {
    return getStartOfMonth(curriculumStore.selectedDate);
  }
};

const getViewEndDate = () => {
  if (currentView.value === "week") {
    return getEndOfWeek(curriculumStore.selectedDate);
  } else {
    return getEndOfMonth(curriculumStore.selectedDate);
  }
};

const getStartOfWeek = (date) => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1);
  start.setDate(diff);
  start.setHours(0, 0, 0, 0);
  return start;
};

const getEndOfWeek = (date) => {
  const end = new Date(getStartOfWeek(date));
  end.setDate(end.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  return end;
};

const getStartOfMonth = (date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  start.setHours(0, 0, 0, 0);
  return start;
};

const getEndOfMonth = (date) => {
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  end.setHours(23, 59, 59, 999);
  return end;
};

const formatDate = (date) => {
  return date.toISOString().split("T")[0];
};

const refreshData = async () => {
  // 使用store的刷新方法，保持筛选参数
  await curriculumStore.refreshCurrentSchedules();
  ElMessage.success("数据已刷新");
};

// 事件处理
const handleViewChange = (view) => {
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
};

const handleDateChange = (date) => {
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
};

const handleSlotClick = (slotData) => {
//   if (canCreateSchedule.value) {
//     // 可以在这里预填充排课表单的时间信息
//     showScheduleDialog.value = true;
//   }
};

const handleDateClick = (date) => {
  // 切换到周视图并显示选中的日期
  curriculumStore.setSelectedDate(new Date(date));
  curriculumStore.setCurrentView("week");
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
};

const handleCourseAction = (actionData) => {
  console.log('=== handleCourseAction 被调用 ===');
  console.log('actionData:', actionData);

  // 处理不同类型的课程操作
  if (actionData && actionData.type === 'consume') {
    console.log('消课操作，设置课程数据:', actionData.course);
    // 消课操作
    selectedCourse.value = actionData.course;
    showConsumeCourseDialog.value = true;
    console.log('showConsumeCourseDialog 设置为 true');
  } else {
    console.log('其他操作，刷新数据');
    // 其他操作后刷新数据，使用store的刷新方法保持筛选参数
    curriculumStore.refreshCurrentSchedules();
  }
};

const handleScheduleSuccess = () => {
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
};

const handleAddCourseClick = () => {
  console.log('=== 点击添加课程按钮 ===');
  console.log('currentTeacherId:', currentTeacherId.value);
  console.log('currentUser:', currentUser.value);
  console.log('route.query:', route.query);
  console.log('props.isDialogMode:', props.isDialogMode);
  console.log('props.teacherId:', props.teacherId);
  console.log('showReviewScheduleDialog before:', showReviewScheduleDialog.value);
  showReviewScheduleDialog.value = true;
  console.log('showReviewScheduleDialog after:', showReviewScheduleDialog.value);

  // 使用nextTick确保组件已经创建
  nextTick(() => {
    console.log('=== nextTick 后检查 ===');
    console.log('传递给ReviewScheduleDialog的teacherId:', currentTeacherId.value);
  });
};

const handleReviewScheduleSuccess = () => {
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
};

const handleConsumeCourseSuccess = () => {
  // 使用store的刷新方法，保持筛选参数
  curriculumStore.refreshCurrentSchedules();
  showConsumeCourseDialog.value = false;
  selectedCourse.value = null;
};

const handleReviewStart = (review) => {
  console.log("开始复习:", review);
};

const handleReviewComplete = (review) => {
  console.log("完成复习:", review);
};

const handleReviewView = (review) => {
  console.log("查看复习详情:", review);
};

// 监听选中日期变化
watch(
  () => curriculumStore.selectedDate,
  () => {
    // 使用store的刷新方法，保持筛选参数
    curriculumStore.refreshCurrentSchedules();
  }
);

// 生命周期
onMounted(async () => {
  // 确保用户信息已加载
  if (!userStore.id) {
    try {
      await userStore.getInfo();
      console.log("用户信息加载完成:", userStore.id, userStore.name);
    } catch (error) {
      console.error("获取用户信息失败:", error);
      ElMessage.error("获取用户信息失败");
    }
  }

  startTimeUpdate();
  fetchScheduleData();
});

onUnmounted(() => {
  stopTimeUpdate();
});
</script>

<style lang="scss" scoped>
.curriculum-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;

  // 弹窗模式样式
  &.dialog-mode {
    height: 100%;
    background: #fff;

    .page-content {
      padding: 16px;
      height: 100%;
    }

    .floating-actions {
      display: none; // 弹窗模式下隐藏悬浮按钮
    }
  }
}

.page-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;

  .title-icon {
    color: #3b82f6;
    font-size: 28px;
  }
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;

  .current-time {
    font-weight: 500;
  }

  .user-info {
    color: #3b82f6;
    font-weight: 500;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.page-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px 24px;
  overflow: hidden;
  min-height: 0; /* 允许flex子元素收缩 */
}

.schedule-section {
  flex: 1;
  min-width: 0;
}

.schedule-card {
  height: 100%;

  :deep(.el-card__body) {
    padding: 0;
    height: calc(100% - 60px);
  }
}

.schedule-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .view-controls {
    .el-radio-button {
      :deep(.el-radio-button__inner) {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .schedule-info {
    display: flex;
    gap: 8px;
  }
}

.schedule-content {
  height: 100%;
  overflow: hidden;
}

.empty-schedule {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-icon {
    font-size: 80px;
    color: #d1d5db;
  }
}

.review-section {
  width: 320px;
  flex-shrink: 0;
}

.floating-actions {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .floating-btn {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: scale(1.1);
    }

    &.schedule-btn {
      order: 1;
    }

    &.review-btn {
      order: 2;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .review-section {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .curriculum-page {
    height: 100vh;
    overflow: hidden;
  }

  .page-header {
    padding: 10px 12px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .page-content {
    flex-direction: column;
    padding: 12px;
    gap: 12px;
    height: calc(100vh - 120px); /* 减去头部高度 */
  }

  .schedule-section {
    flex: 1;
    min-height: 0;
    order: 1;
  }

  .review-section {
    width: 100%;
    height: 300px; /* 固定高度，避免占用过多空间 */
    flex-shrink: 0;
    order: 2;
  }

  .schedule-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    padding: 8px 12px;
  }

  .schedule-info {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 18px;

    .title-icon {
      font-size: 22px;
    }
  }

  .header-meta {
    font-size: 11px;
  }

  .page-content {
    padding: 8px;
    gap: 8px;
    height: calc(100vh - 100px);
  }

  .schedule-toolbar {
    padding: 6px 8px;
  }

  .view-controls {
    width: 100%;

    .el-radio-group {
      width: 100%;

      .el-radio-button {
        flex: 1;

        :deep(.el-radio-button__inner) {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }

  .schedule-info {
    gap: 4px;

    .el-tag {
      font-size: 10px;
      padding: 0 4px;
      height: 20px;
      line-height: 20px;
    }

    .el-button {
      width: 28px;
      height: 28px;

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .review-section {
    height: 250px; /* 更小的高度 */
  }

  .floating-actions {
    bottom: 16px;
    right: 16px;

    .floating-btn {
      width: 48px;
      height: 48px;
    }
  }
}
</style>
