<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="upload-content">
      <!-- 复习信息 -->
      <div class="review-info">
        <h4>{{ reviewData?.reviewName }}</h4>
        <p class="review-meta">
          <span class="review-type">{{ reviewData?.reviewType }}</span>
          <span class="student-name">{{ reviewData?.studentName }}</span>
        </p>
      </div>

      <!-- 上传表单 -->
      <el-form
        v-if="mode === 'upload'"
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        label-width="100px"
      >
        <el-form-item label="上传图片" prop="imageFiles" required>
          <el-upload
            ref="uploadRef"
            :file-list="uploadForm.imageFiles"
            :auto-upload="false"
            :limit="9"
            :multiple="true"
            :show-file-list="true"
            list-type="picture-card"
            accept="image/png,image/jpg,image/jpeg"
            :before-upload="beforeUpload"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            :on-change="handleFileChange"
          >
            <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-instructions">
            <div class="upload-tip">
              <span style="color: #f56c6c;">* 必须上传至少2张图片</span>
            </div>
            <div class="upload-description">
              <p><strong>上传说明：</strong></p>
              <p>（1）<strong>复习前提醒证明：</strong>家长群的提醒截图（包含家长群名称，发送复习提醒或者会议的具体链接）</p>
              <p>（2）<strong>复习后反馈证明：</strong>家长群的反馈截图（包含家长群名称，抗遗忘的反馈）</p>
            </div>
            <div class="format-tip">
              支持上传2-9张图片，每张图片不超过10MB，支持PNG、JPG、JPEG格式
            </div>
          </div>
        </el-form-item>

        <el-form-item label="说明文字" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入复习完成情况的说明（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <!-- 查看内容 -->
      <div v-if="mode === 'view'" class="view-content">
        <div v-if="reviewData?.hasUploaded" class="uploaded-content">
          <!-- 图片展示 -->
          <div v-if="reviewData?.imageUrls?.length" class="image-gallery">
            <h5>上传的图片</h5>
            <div class="image-grid">
              <div
                v-for="(imageUrl, index) in reviewData.imageUrls"
                :key="index"
                class="image-item"
                @click="previewImage(imageUrl)"
              >
                <el-image
                  :src="imageUrl"
                  fit="cover"
                  class="uploaded-image"
                  :preview-src-list="reviewData.imageUrls"
                  :initial-index="index"
                />
              </div>
            </div>
          </div>

          <!-- 说明文字 -->
          <div v-if="reviewData?.description" class="description-section">
            <h5>说明文字</h5>
            <p class="description-text">{{ reviewData.description }}</p>
          </div>

          <!-- 上传时间 -->
          <div class="upload-time">
            <span class="time-label">上传时间：</span>
            <span class="time-value">{{ formatDateTime(reviewData.uploadedTime) }}</span>
          </div>
        </div>

        <div v-else class="no-upload">
          <el-empty description="暂无上传内容" :image-size="100" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode === 'upload'"
          type="primary"
          :loading="uploading"
          @click="handleUpload"
        >
          确认上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadReviewCompletionApi, viewReviewUploadApi } from "@/api/curriculum";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  reviewScheduleId: {
    type: String,
    default: "",
  },
  mode: {
    type: String,
    default: "upload", // 'upload' | 'view'
    validator: (value) => ["upload", "view"].includes(value),
  },
});

const emit = defineEmits(["update:modelValue", "uploaded", "close"]);

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const uploadFormRef = ref();
const uploadRef = ref();
const uploading = ref(false);
const reviewData = ref(null);

// 上传表单
const uploadForm = ref({
  imageFiles: [],
  description: "",
});

// 表单验证规则
const uploadRules = {
  imageFiles: [
    { required: true, message: "请至少上传两张图片", trigger: "change" },
    {
      validator: (rule, value, callback) => {
        if (!value || value.length < 2) {
          callback(new Error("请至少上传两张图片"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
};

// 计算属性
const dialogTitle = computed(() => {
  if (props.mode === "upload") {
    return "上传复习完成情况";
  } else {
    return "查看复习完成情况";
  }
});

// 监听对话框打开
watch(visible, async (newVal) => {
  console.log(
    "🔄 [抗遗忘复习] ReviewUploadDialog visible 变化:",
    newVal,
    "reviewScheduleId:",
    props.reviewScheduleId
  );
  if (newVal && props.reviewScheduleId) {
    await loadReviewData();
  }
});

// 方法
const loadReviewData = async () => {
  try {
    const response = await viewReviewUploadApi(props.reviewScheduleId);
    if (response.code === 200) {
      reviewData.value = response.data;
    } else {
      ElMessage.error(response.message || "获取复习数据失败");
    }
  } catch (error) {
    console.error("获取复习数据失败:", error);
    ElMessage.error("获取复习数据失败，请检查网络连接");
  }
};

// 文件上传前验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith("image/");
  const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isImage || !isValidType) {
    ElMessage.error("只能上传PNG、JPG、JPEG格式的图片!");
    return false;
  }
  if (!isLt10M) {
    ElMessage.error("图片大小不能超过10MB!");
    return false;
  }
  return false; // 阻止自动上传，我们手动处理
};

// 文件数量超出限制
const handleExceed = () => {
  ElMessage.warning("最多只能上传9张图片!");
};

// 文件移除
const handleRemove = (file) => {
  const index = uploadForm.value.imageFiles.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    uploadForm.value.imageFiles.splice(index, 1);
  }
};

// 文件变化
const handleFileChange = (file, fileList) => {
  uploadForm.value.imageFiles = fileList;
};

const handleUpload = async () => {
  if (!uploadFormRef.value) return;

  try {
    await uploadFormRef.value.validate();

    if (uploadForm.value.imageFiles.length < 2) {
      ElMessage.error("请至少上传两张图片");
      return;
    }

    await ElMessageBox.confirm(
      "确定要上传这些内容吗？上传后将标记复习为已完成。",
      "确认上传",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    uploading.value = true;

    // 创建FormData
    const formData = new FormData();
    formData.append("reviewScheduleId", props.reviewScheduleId);
    formData.append("description", uploadForm.value.description || "");

    // 添加图片文件
    uploadForm.value.imageFiles.forEach((fileItem, index) => {
      if (fileItem.raw) {
        formData.append("images", fileItem.raw);
      }
    });

    console.log("🔄 [抗遗忘复习] 上传数据:", {
      reviewScheduleId: props.reviewScheduleId,
      description: uploadForm.value.description,
      imageCount: uploadForm.value.imageFiles.length,
    });

    const response = await uploadReviewCompletionApi(formData);

    if (response.code === 200) {
      ElMessage.success("上传成功");
      emit("uploaded", response.data);
      handleClose();
    } else {
      ElMessage.error(response.message || "上传失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("上传失败:", error);
      ElMessage.error("上传失败，请重试");
    }
  } finally {
    uploading.value = false;
  }
};

const handleClose = () => {
  // 重置表单
  if (uploadFormRef.value) {
    uploadFormRef.value.resetFields();
  }
  uploadForm.value = {
    imageFiles: [],
    description: "",
  };
  reviewData.value = null;

  emit("close");
  visible.value = false;
};

const previewImage = (imageUrl) => {
  // 图片预览功能已由 el-image 的 preview-src-list 属性处理
};

const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return new Date(dateTime).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};
</script>

<style scoped>
.upload-content {
  padding: 10px 0;
}

.review-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.review-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.review-meta {
  margin: 0;
  display: flex;
  gap: 15px;
  color: #606266;
  font-size: 14px;
}

.review-type {
  background-color: #e1f3d8;
  color: #67c23a;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.upload-instructions {
  margin-top: 8px;
}

.upload-instructions .upload-tip {
  font-size: 12px;
  margin-bottom: 8px;
}

.upload-instructions .upload-description {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.upload-instructions .upload-description p {
  margin: 0 0 8px 0;
}

.upload-instructions .upload-description p:last-child {
  margin-bottom: 0;
}

.upload-instructions .upload-description p strong {
  color: #303133;
  font-weight: 600;
}

.upload-instructions .upload-description p:first-child {
  color: #606266;
  font-weight: 600;
  margin-bottom: 10px;
}

.upload-instructions .upload-description p:not(:first-child) {
  color: #666;
  padding-left: 8px;
  position: relative;
}

.upload-instructions .upload-description p:not(:first-child)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8px;
  width: 3px;
  height: 3px;
  background-color: #909399;
  border-radius: 50%;
}

.upload-instructions .format-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.view-content {
  min-height: 200px;
}

.uploaded-content > * + * {
  margin-top: 20px;
}

.image-gallery h5,
.description-section h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.image-item {
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  transition: transform 0.2s;
}

.image-item:hover {
  transform: scale(1.02);
}

.uploaded-image {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.description-section {
  margin-bottom: 20px;
}

.description-text {
  margin: 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
}

.upload-time {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.time-label {
  font-weight: 500;
}

.time-value {
  color: #606266;
}

.no-upload {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
