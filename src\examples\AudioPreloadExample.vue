<template>
  <div class="audio-preload-example">
    <h3>音频预加载示例</h3>
    
    <!-- 预加载状态显示 -->
    <div class="preload-status" v-if="audioPreloader">
      <p>预加载状态: {{ audioPreloader.isLoading.value ? '加载中...' : '已完成' }}</p>
      <p>已加载: {{ audioPreloader.loadedCount.value }} / {{ audioPreloader.totalCount.value }}</p>
      <p v-if="audioPreloader.errors.value.length > 0" class="error">
        错误: {{ audioPreloader.errors.value.join(', ') }}
      </p>
    </div>
    
    <!-- 音频播放按钮 -->
    <div class="audio-controls">
      <button @click="playAudio(audioUrls.uk)" :disabled="!audioPreloader || audioPreloader.isLoading.value">
        播放英式发音
      </button>
      <button @click="playAudio(audioUrls.us)" :disabled="!audioPreloader || audioPreloader.isLoading.value">
        播放美式发音
      </button>
    </div>
    
    <!-- 控制按钮 -->
    <div class="control-buttons">
      <button @click="startPreload">开始预加载</button>
      <button @click="clearCache">清除缓存</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAudioPreloader, playAudioUtil } from '@/api/course/util';

// 示例音频URL（请替换为实际的音频URL）
const audioUrls = {
  uk: 'https://example.com/audio/word_uk.mp3',
  us: 'https://example.com/audio/word_us.mp3'
};

// 音频预加载器
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

// 开始预加载
const startPreload = async () => {
  const urls = Object.values(audioUrls).filter(url => url && url.trim() !== '');
  
  if (urls.length > 0) {
    console.log('开始预加载音频文件:', urls);
    audioPreloader = useAudioPreloader(urls);
    await audioPreloader.preloadAudios();
    console.log('音频预加载完成');
  }
};

// 播放音频
const playAudio = (url: string) => {
  if (url) {
    playAudioUtil(url);
  }
};

// 清除缓存
const clearCache = () => {
  if (audioPreloader) {
    audioPreloader.clearCache();
    console.log('音频缓存已清除');
  }
};

// 组件挂载时自动开始预加载
onMounted(() => {
  startPreload();
});
</script>

<style scoped>
.audio-preload-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.preload-status {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.preload-status p {
  margin: 5px 0;
}

.error {
  color: #e74c3c;
}

.audio-controls {
  margin-bottom: 20px;
}

.audio-controls button,
.control-buttons button {
  margin: 5px;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  background: #3498db;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.audio-controls button:hover,
.control-buttons button:hover {
  background: #2980b9;
}

.audio-controls button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}
</style>