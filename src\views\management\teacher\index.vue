<template>
  <div class="teacher-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-left">
          <el-form-item label="教师姓名">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入教师姓名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入手机号码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="教学组">
            <el-select
              v-model="searchForm.groupId"
              placeholder="请选择教学组"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="group in teachingGroups"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="在职" value="active" />
              <el-option label="离职" value="inactive" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </div>
        <div class="form-right">
          <el-button
            size="small"
            type="primary"
            @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增教师
          </el-button>
          <el-button 
            size="small"
            type="success"
            @click="handleImport"
            v-hasPermi="['management:teacher:import']"
          >
            导入教师
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="handleDownloadTemplate"
            v-hasPermi="['management:teacher:import']"
          >
            下载模板
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="handleExport"
            v-hasPermi="['management:teacher:export']"
          >
            导出教师
          </el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 教师列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="teachers"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="教师姓名" min-width="120" fixed="left" />
        <el-table-column prop="phone" label="手机号码" min-width="140" />
        <el-table-column prop="groupName" label="教学组" min-width="150">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ row.groupName || '-' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="false" prop="subjects" label="教授学科" min-width="180">
          <template #default="{ row }">
            <div class="subjects-tags">
              <el-tag
                v-for="subject in (row.subjects || [])"
                :key="subject"
                size="small"
                class="subject-tag"
              >
                {{ subject }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" label="学生数" min-width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.studentCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="false" prop="totalHours" label="总课时" min-width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.totalHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="false" prop="completedHours" label="已完成课时" min-width="120" align="center">
          <template #default="{ row }">
            <span>{{ row.completedHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="入职时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="380" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">
              查看详情
            </el-button>
            <el-button type="success" link @click="handleViewSchedule(row)">
              查看课表
            </el-button>
            <el-button
              type="info"
              link
              @click="handleEditTimeSlots(row)"
              v-hasRole="['admin', 'hr', 'teaching_group_leader', 'teaching_group_admin', 'teacher']"
            >
              上课时间
            </el-button>
            <el-button type="warning" link @click="handleAssignStudents(row)">
              分配学生
            </el-button>
            <!-- 停课/复课按钮 -->
            <el-button
              v-if="row.status === 'active' || row.status === '0'"
              type="danger"
              link
              @click="handleSuspendTeacher(row)"
            >
              停止接课
            </el-button>
            <el-button
              v-else
              type="success"
              link
              @click="handleResumeTeacher(row)"
            >
              恢复接课
            </el-button>
            <el-button type="primary" link @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="warning" link @click="handleResetPassword(row)">
              重置密码
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑教师对话框 -->
    <CreateEditTeacherDialog
      v-model="dialogVisible"
      :teacher="currentTeacher"
      :teaching-groups="teachingGroups"
      @success="handleDialogSuccess"
    />

    <!-- 教师详情对话框 -->
    <TeacherDetailDialog
      v-model="detailDialogVisible"
      :teacher="currentTeacher"
      @edit="handleEdit"
    />

    <!-- 教师课表对话框 -->
    <TeacherCourseScheduleDialog
      v-model="scheduleDialogVisible"
      :teacher="currentTeacher"
    />

    <!-- 分配学生对话框 -->
    <AssignStudentsDialog
      v-model="assignStudentsDialogVisible"
      :teacher="currentTeacher"
      @success="handleAssignStudentsSuccess"
      @add-student="handleAddStudent"
    />

    <!-- 时间段编辑对话框 -->
    <TeacherTimeSlotEditDialog
      v-model="timeSlotEditDialogVisible"
      :teacher="currentTeacher"
      @success="handleTimeSlotEditSuccess"
    />
  </div>
</template>

<script setup name="TeacherManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Upload, Download } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import { exportTeachersApi, importTeachersApi, updateTeacherStatusApi } from '@/api/management/teachingGroup'
import { checkPermi } from '@/utils/permission'
import * as XLSX from 'xlsx'
import CreateEditTeacherDialog from './components/CreateEditTeacherDialog.vue'
import TeacherDetailDialog from './components/TeacherDetailDialog.vue'
import TeacherCourseScheduleDialog from './components/TeacherCourseScheduleDialog.vue'
import TeacherTimeSlotEditDialog from './components/TeacherTimeSlotEditDialog.vue'
import AssignStudentsDialog from '../teaching-group/components/AssignStudentsDialog.vue'

// Store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const searchForm = reactive({
  name: '',
  phone: '',
  groupId: '',
  status: ''
})

const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
})

const teachers = ref([])
const teachingGroups = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const timeSlotEditDialogVisible = ref(false)
const assignStudentsDialogVisible = ref(false)
const currentTeacher = ref(null)

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // 获取教学组列表
    await teachingGroupStore.fetchTeachingGroups()
    teachingGroups.value = teachingGroupStore.teachingGroups

    // 获取教师列表（支持搜索和分页）
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      phone: searchForm.phone,
      groupId: searchForm.groupId,
      status: searchForm.status
    }

    const result = await teachingGroupStore.fetchTeachers(params)
    teachers.value = result.records || result.rows || []
    pagination.total = result.total || 0
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}



const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    groupId: '',
    status: ''
  })
  pagination.pageNum = 1
  fetchData()
}

const handleCreate = () => {
  currentTeacher.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  currentTeacher.value = { ...row }
  dialogVisible.value = true
}

const handleView = (row) => {
  currentTeacher.value = { ...row }
  detailDialogVisible.value = true
}

const handleViewSchedule = (row) => {
  // 使用弹窗显示教师课表
  currentTeacher.value = { ...row }
  scheduleDialogVisible.value = true
}

const handleEditTimeSlots = (row) => {
  // 使用弹窗显示时间段编辑功能
  currentTeacher.value = { ...row }
  timeSlotEditDialogVisible.value = true
}

const handleAssignStudents = (row) => {
  // 使用弹窗显示分配学生功能
  currentTeacher.value = { ...row }
  assignStudentsDialogVisible.value = true
}

const handleAssignStudentsSuccess = () => {
  assignStudentsDialogVisible.value = false
  fetchData()
}

const handleAddStudent = () => {
  // 这里可以跳转到学生管理页面或打开新增学生弹窗
  console.log('新增学生')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除教师"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const success = await teachingGroupStore.deleteTeacher(row.id)
    if (success) {
      fetchData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 重置密码
const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置教师"${row.name}"的密码吗？密码将重置为：654321`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const success = await teachingGroupStore.resetTeacherPassword(row.id)
    if (success) {
      ElMessage.success('重置密码成功，新密码为：654321')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
    }
  }
}



const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  fetchData()
}



const handleTimeSlotEditSuccess = () => {
  timeSlotEditDialogVisible.value = false
  // 时间段编辑成功后可以选择是否刷新数据
  // fetchData()
}

const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'success',  // 正常
    '1': 'danger',   // 停课
    'active': 'success',
    'inactive': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    '0': '正常',
    '1': '停课',
    'active': '正常',
    'inactive': '停课'
  }
  return statusMap[status] || status
}

// 导入教师
const handleImport = () => {
  // 检查权限
  if (!checkPermi(['management:teacher:import'])) {
    ElMessage.error('您没有导入教师的权限')
    return
  }

  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (e) => {
    const file = e.target.files[0]
    if (!file) return

    const loadingInstance = ElMessage({
      message: '正在解析Excel文件...',
      type: 'info',
      duration: 0
    })

    try {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data)
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet)

      // 验证Excel格式
      if (jsonData.length === 0) {
        throw new Error('Excel文件为空，请检查文件内容')
      }

      // 验证必需的列
      const requiredColumns = ['姓名', '昵称', '性别', '电话']
      const firstRow = jsonData[0]
      const missingColumns = requiredColumns.filter(col => !(col in firstRow))
      if (missingColumns.length > 0) {
        throw new Error(`Excel文件缺少必需的列：${missingColumns.join(', ')}`)
      }

      // 转换数据格式
      const teacherDataList = []
      const errors = []

      jsonData.forEach((row, index) => {
        const rowNum = index + 1

        try {
          // 验证必填字段
          if (!row['姓名'] || !row['昵称'] || !row['性别'] || !row['电话']) {
            errors.push(`第${rowNum}行：姓名、昵称、性别、电话不能为空`)
            return
          }

          // 验证电话号码格式
          const phone = String(row['电话']).trim()
          if (!/^1[3-9]\d{9}$/.test(phone)) {
            errors.push(`第${rowNum}行：电话号码格式不正确`)
            return
          }

          // 转换性别
          let gender = '2' // 默认未知
          const genderText = String(row['性别']).trim()
          if (genderText === '男') gender = '0'
          else if (genderText === '女') gender = '1'
          else {
            errors.push(`第${rowNum}行：性别只能是"男"或"女"`)
            return
          }

          teacherDataList.push({
            name: String(row['姓名']).trim(),
            nickname: String(row['昵称']).trim(),
            gender: gender,
            phone: phone
          })
        } catch (err) {
          errors.push(`第${rowNum}行：数据格式错误`)
        }
      })

      // 如果有验证错误，显示错误信息
      if (errors.length > 0) {
        const errorMsg = errors.slice(0, 5).join('\n') + (errors.length > 5 ? `\n...还有${errors.length - 5}个错误` : '')
        throw new Error(errorMsg)
      }

      if (teacherDataList.length === 0) {
        throw new Error('没有有效的数据可以导入')
      }

      // 更新加载提示
      loadingInstance.close()
      const importingInstance = ElMessage({
        message: `正在导入 ${teacherDataList.length} 名教师...`,
        type: 'info',
        duration: 0
      })

      try {
        // 调用导入API
        const result = await importTeachersApi(teacherDataList)

        importingInstance.close()

        if (result.data.successCount > 0) {
          ElMessage.success(`成功导入 ${result.data.successCount} 名教师`)
          fetchData() // 刷新列表
        }

        if (result.data.failureCount > 0) {
          ElMessage.warning(`${result.data.failureCount} 名教师导入失败：${result.data.errorMessages.join(', ')}`)
        }
      } catch (importError) {
        importingInstance.close()
        console.error('导入API调用失败:', importError)
        ElMessage.error('导入失败: ' + importError.message)
      }
    } catch (error) {
      loadingInstance.close()
      console.error('导入失败:', error)
      ElMessage.error('导入失败: ' + error.message)
    }
  }
  input.click()
}

// 下载导入模板
const handleDownloadTemplate = () => {
  // 检查权限
  if (!checkPermi(['management:teacher:import'])) {
    ElMessage.error('您没有下载模板的权限')
    return
  }

  // 创建模板数据
  const templateData = [
    {
      '姓名': '张三',
      '昵称': '张老师',
      '性别': '男',
      '电话': '13800138001'
    },
    {
      '姓名': '李四',
      '昵称': '李老师',
      '性别': '女',
      '电话': '13800138002'
    }
  ]

  // 创建工作簿
  const worksheet = XLSX.utils.json_to_sheet(templateData)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '教师导入模板')

  // 下载文件
  const fileName = '教师导入模板.xlsx'
  XLSX.writeFile(workbook, fileName)

  ElMessage.success('模板下载成功')
}

// 导出教师
const handleExport = async () => {
  // 检查权限
  if (!checkPermi(['management:teacher:export'])) {
    ElMessage.error('您没有导出教师的权限')
    return
  }

  try {
    const params = {
      name: searchForm.name,
      phone: searchForm.phone,
      groupId: searchForm.groupId,
      status: searchForm.status
    }

    const result = await exportTeachersApi(params)

    // 转换数据格式用于Excel导出
    const exportData = result.data.map(teacher => ({
      '姓名': teacher.name,
      '性别': teacher.gender,
      '电话': teacher.phone,
      '学历': teacher.education || '',
      '专业': teacher.major || '',
      '大学属性': teacher.universityType || '',
      '教资级别': teacher.teachingCertificateLevel || '',
      '英语资质': teacher.englishQualification || ''
    }))

    // 创建工作簿
    const worksheet = XLSX.utils.json_to_sheet(exportData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '教师列表')

    // 下载文件
    const fileName = `教师列表_${new Date().toISOString().slice(0, 10)}.xlsx`
    XLSX.writeFile(workbook, fileName)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 停课教师
const handleSuspendTeacher = async (teacher) => {
  try {
    await ElMessageBox.confirm(
      `确定要对教师 ${teacher.name} 进行停课操作吗？停课后该教师将无法正常授课。`,
      '停课确认',
      {
        confirmButtonText: '确定停课',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const result = await updateTeacherStatusApi(teacher.id, 'inactive')
    if (result.code === 200) {
      ElMessage.success('教师停课成功')
      fetchData() // 刷新列表
    } else {
      ElMessage.error(result.msg || '停课失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停课失败:', error)
      ElMessage.error('停课失败')
    }
  }
}

// 复课教师
const handleResumeTeacher = async (teacher) => {
  try {
    await ElMessageBox.confirm(
      `确定要对教师 ${teacher.name} 进行复课操作吗？复课后该教师将恢复正常授课。`,
      '复课确认',
      {
        confirmButtonText: '确定复课',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const result = await updateTeacherStatusApi(teacher.id, 'active')
    if (result.code === 200) {
      ElMessage.success('教师复课成功')
      fetchData() // 刷新列表
    } else {
      ElMessage.error(result.msg || '复课失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复课失败:', error)
      ElMessage.error('复课失败')
    }
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.teacher-management-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
  width: 100%;
  box-sizing: border-box;

  .search-card {
    margin-bottom: 20px;

    :deep(.el-card__body) {
      padding: 16px 20px;
    }

    .search-form {
      margin-bottom: 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .form-left {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 0;
      }

      .form-right {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      }
    }
  }

  .table-card {
    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.el-table) {
      width: 100% !important;
    }

    :deep(.el-table__body-wrapper) {
      width: 100% !important;
    }

    .subjects-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .subject-tag {
        margin: 0;
      }
    }

    .pagination-container {
      padding: 16px 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
