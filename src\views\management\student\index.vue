<template>
  <div class="student-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="年级">
          <el-select
            v-model="searchForm.grade"
            placeholder="请选择年级"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="grade in GRADE_OPTIONS"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value.toString()"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="在读" value="active" />
            <el-option label="暂停" value="inactive" />
            <el-option label="毕业" value="graduated" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleCreate"
              v-hasRole="['admin', 'hr', 'teaching_group_leader', 'teaching_group_admin']"
          >
            <el-icon><Plus /></el-icon>
            新增学生
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 学生列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="students"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="学生姓名" width="100" fixed="left" />
        <el-table-column prop="phone" label="手机号码" width="120" />
        <el-table-column prop="grade" label="年级" width="100">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ getGradeText(row.grade) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="school" label="学校" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.school || '-' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="teacherName" label="任课教师" width="100">
          <template #default="{ row }">
            <span>{{ row.teacherName || '-' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="totalHours" label="总课时" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.totalHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="consumedHours" label="已消课时" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.consumedHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remainingHours" label="剩余课时" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getRemainingHoursTagType(row.remainingHours)" 
              size="small"
            >
              {{ row.remainingHours || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">
              查看详情
            </el-button>
            <el-button type="success" link @click="handleViewSchedule(row)">
              查看课表
            </el-button>
            <el-button type="warning" link @click="handleMatchTeacher(row)"
              v-hasRole="['admin', 'hr', 'teaching_group_leader', 'teaching_group_admin']"
            >
              匹配老师
            </el-button>
            <el-button type="primary" link @click="handleEdit(row)"
              v-hasRole="['admin', 'hr', 'teaching_group_leader', 'teaching_group_admin']"
            >
              编辑
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)"
              v-hasRole="['admin', 'hr', 'teaching_group_leader', 'teaching_group_admin']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑学生对话框 -->
    <CreateEditStudentDialog
      v-model="dialogVisible"
      :student="currentStudent"
      @success="handleDialogSuccess"
    />

    <!-- 学生详情对话框 -->
    <StudentViewDialog
      v-model="detailDialogVisible"
      :student="currentStudent"
      @edit="handleEdit"
    />

    <!-- 学生课表对话框 -->
    <StudentCourseScheduleDialog
      v-model="scheduleDialogVisible"
      :student="currentStudent"
    />

    <!-- 老师匹配对话框 -->
    <el-dialog
      v-model="teacherMatchDialogVisible"
      title="匹配老师"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="teacher-match-dialog"
      @close="handleTeacherMatchClose"
    >
      <div style="height: 80vh; overflow: hidden;">
        <TeacherMatchPage
          :student-info="currentStudent"
          :is-dialog-mode="true"
          :mode="'direct'"
          @success="handleTeacherMatchSuccess"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="StudentManagement">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { useStudentStore } from '@/stores/student'
import { getGradeText, GRADE_OPTIONS } from '@/utils/gradeUtils'
import CreateEditStudentDialog from './components/CreateEditStudentDialog.vue'
import StudentViewDialog from './components/StudentViewDialog.vue'
import StudentCourseScheduleDialog from './components/StudentCourseScheduleDialog.vue'
import TeacherMatchPage from '@/views/management/teacher-match/index.vue'

// Store
const studentStore = useStudentStore()

// 响应式数据
const searchForm = reactive({
  name: '',
  phone: '',
  grade: '',
  status: ''
})

const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const teacherMatchDialogVisible = ref(false)
const currentStudent = ref(null)

// 计算属性
const students = computed(() => studentStore.students)
const loading = computed(() => studentStore.loading)
const pagination = computed(() => studentStore.pagination)

// 方法
const fetchData = async () => {
  await studentStore.fetchStudents(searchForm)
}



const handleSearch = () => {
  studentStore.setPagination(1, pagination.value.pageSize)
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    grade: '',
    status: ''
  })
  studentStore.setPagination(1, pagination.value.pageSize)
  fetchData()
}

const handleCreate = () => {
  currentStudent.value = null
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    // 获取最新的学生详细信息
    const latestStudentData = await studentStore.fetchStudentDetail(row.id)
    if (latestStudentData) {
      currentStudent.value = latestStudentData
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取学生详情失败:', error)
    ElMessage.error('获取学生详情失败')
  }
}

const handleView = async (row) => {
  try {
    // 获取最新的学生详细信息
    const latestStudentData = await studentStore.fetchStudentDetail(row.id)
    if (latestStudentData) {
      currentStudent.value = latestStudentData
      detailDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取学生详情失败:', error)
    ElMessage.error('获取学生详情失败')
  }
}

const handleViewSchedule = (row) => {
  // 使用弹窗显示学生课表
  currentStudent.value = { ...row }
  scheduleDialogVisible.value = true
}

const handleMatchTeacher = (row) => {
  // 使用弹窗模式显示老师匹配页面
  currentStudent.value = { ...row }
  teacherMatchDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学生"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const success = await studentStore.deleteStudent(row.id)
    if (success) {
      fetchData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}



const handleSizeChange = (size) => {
  studentStore.setPagination(1, size)
  fetchData()
}

const handleCurrentChange = (page) => {
  studentStore.setPagination(page, pagination.value.pageSize)
  fetchData()
}

// 老师匹配对话框事件处理
const handleTeacherMatchSuccess = () => {
  teacherMatchDialogVisible.value = false
  currentStudent.value = null
  // 可以在这里刷新学生列表或显示成功消息
  fetchData()
}

const handleTeacherMatchClose = () => {
  teacherMatchDialogVisible.value = false
  currentStudent.value = null
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  fetchData()
}

const getRemainingHoursTagType = (hours) => {
  if (hours <= 5) return 'danger'
  if (hours <= 10) return 'warning'
  return 'success'
}

const getStatusTagType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '在读',
    'inactive': '暂停',
    'graduated': '毕业'
  }
  return statusMap[status] || status
}

// getGradeText 函数已移至 @/utils/gradeUtils

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.student-management-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-left {
      h2 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }

      .stats-cards {
        display: flex;
        gap: 16px;

        .stats-card {
          min-width: 120px;

          :deep(.el-card__body) {
            padding: 16px;
          }

          .stats-content {
            text-align: center;

            .stats-number {
              font-size: 24px;
              font-weight: 600;
              color: #409eff;
              margin-bottom: 4px;
            }

            .stats-label {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }

    .header-right {
      margin-top: 40px;
    }
  }

  .search-card {
    margin-bottom: 20px;

    :deep(.el-card__body) {
      padding: 16px 20px;
    }

    .el-form {
      margin-bottom: 0;
    }
  }

  .table-card {
    :deep(.el-card__body) {
      padding: 0;
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;

      .table-actions {
        display: flex;
        gap: 8px;
      }
    }

    .pagination-container {
      padding: 16px 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
