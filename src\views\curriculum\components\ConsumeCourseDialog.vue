<template>
  <el-dialog
    v-model="visible"
    title="消课"
    width="700px"
    :before-close="handleClose"
    class="consume-course-dialog"
    top="5vh"
    :close-on-click-modal="false"
  >
    <!-- 课程信息展示 -->
    <el-card class="course-info-card" shadow="never">
      <template #header>
        <span class="course-info-title">课程信息</span>
      </template>
      <el-descriptions :column="2" size="small">
        <el-descriptions-item label="学生">{{ courseInfo.studentName }}</el-descriptions-item>
        <el-descriptions-item label="学科">{{ courseInfo.subject }}</el-descriptions-item>
        <el-descriptions-item label="课型">{{ courseInfo.specification }}</el-descriptions-item>
        <el-descriptions-item label="排课时长">{{ courseInfo.durationMinutes }}分钟</el-descriptions-item>
        <el-descriptions-item label="排课日期">{{ courseInfo.courseDate }}</el-descriptions-item>
        <el-descriptions-item label="上课时间段">{{ courseInfo.timeRange }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      class="consume-course-form"
    >
      <!-- 上课日期 -->
      <el-form-item label="上课日期" prop="courseDate">
        <el-date-picker
          v-model="form.courseDate"
          type="date"
          placeholder="选择上课日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 开始时间和结束时间同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime" label-width="80px">
              <el-time-select
                v-model="form.startTime"
                start="06:00"
                end="24:00"
                step="00:05"
                placeholder="选择开始时间"
                style="width: 100%"
                :max-time="getMaxStartTime()"
                @change="onStartTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime" label-width="80px">
              <el-time-select
                v-model="form.endTime"
                start="06:00"
                end="24:00"
                step="00:05"
                placeholder="选择结束时间"
                style="width: 100%"
                :min-time="getMinEndTime()"
                @change="onEndTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 录屏链接 -->
      <el-form-item label="录屏链接">
        <el-input
          v-model="form.recordingUrl"
          placeholder="请输入录屏链接（可选）"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 上传图片 -->
      <el-form-item label="上传图片" prop="images">
        <ImageUpload
          v-model="form.images"
          :limit="10"
          :file-size="5"
          :file-type="['png', 'jpg', 'jpeg']"
          :is-show-tip="true"
        />
        <div class="upload-instructions">
          <div class="upload-tip">
            <span style="color: #f56c6c;">* 至少上传2张图片</span>
          </div>
          <div class="upload-description">
            <p><strong>上传说明：</strong></p>
            <p>（1）<strong>上课前提醒证明：</strong>家长群截图（包含家长群名称，发送上课会议的具体链接）</p>
            <p>（2）<strong>上课后反馈证明：</strong>家长群的反馈截图（包含家长群名称，这节课的具体内容反馈）</p>
          </div>
        </div>
      </el-form-item>

      <!-- 说明 -->
      <el-form-item label="说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入说明信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          确定消课
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useCurriculumStore } from "@/stores/curriculum";
import ImageUpload from "@/components/ImageUpload/index.vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  course: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const curriculumStore = useCurriculumStore();

// 响应式数据
const formRef = ref(null);
const submitting = ref(false);

// 表单数据
const form = ref({
  courseDate: "",
  startTime: "",
  endTime: "",
  recordingUrl: "",
  images: "",
  description: "",
});

// 表单验证规则
const rules = {
  courseDate: [{ required: true, message: "请选择上课日期", trigger: "change" }],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  images: [
    { required: true, message: "请上传图片", trigger: "change" },
    {
      validator: (_rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error("请至少上传2张图片"));
        } else {
          // 将字符串转换为数组进行验证
          const imageArray = typeof value === 'string' ? value.split(',').filter(url => url.trim()) : value;
          if (imageArray.length < 2) {
            callback(new Error("请至少上传2张图片"));
          } else {
            callback();
          }
        }
      },
      trigger: "change"
    }
  ],
};

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 课程信息
const courseInfo = computed(() => {
  if (!props.course || !props.course.id) {
    return {
      studentName: '',
      subject: '',
      specification: '',
      durationMinutes: 0,
      courseDate: '',
      timeRange: '',
      scheduledStartTime: '',
      scheduledEndTime: ''
    };
  }

  // 获取开始和结束时间
  const startTimeStr = props.course.scheduledStartTime || props.course.startTime;
  const endTimeStr = props.course.scheduledEndTime || props.course.endTime;

  // 计算时长
  let durationMinutes = props.course.scheduledDuration || props.course.durationMinutes || 0;

  // 如果没有时长但有开始结束时间，则计算时长
  if (!durationMinutes && startTimeStr && endTimeStr) {
    try {
      const startTime = new Date(startTimeStr);
      const endTime = new Date(endTimeStr);
      if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
        durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
      }
    } catch (error) {
      console.error('计算课程时长失败:', error);
    }
  }

  // 格式化日期和时间段
  let courseDate = '';
  let timeRange = '';

  if (startTimeStr) {
    try {
      const startTime = new Date(startTimeStr);
      if (!isNaN(startTime.getTime())) {
        // 提取日期
        courseDate = startTime.toISOString().split('T')[0];

        // 格式化时间段
        const startHour = String(startTime.getHours()).padStart(2, '0');
        const startMinute = String(startTime.getMinutes()).padStart(2, '0');

        if (endTimeStr) {
          const endTime = new Date(endTimeStr);
          if (!isNaN(endTime.getTime())) {
            const endHour = String(endTime.getHours()).padStart(2, '0');
            const endMinute = String(endTime.getMinutes()).padStart(2, '0');
            timeRange = `${startHour}:${startMinute} ~ ${endHour}:${endMinute}`;
          }
        }
      }
    } catch (error) {
      console.error('解析课程时间失败:', error);
    }
  }

  return {
    studentName: props.course.studentName || '',
    subject: props.course.subject || '',
    specification: props.course.specification || '',
    durationMinutes: durationMinutes,
    courseDate: courseDate,
    timeRange: timeRange,
    scheduledStartTime: startTimeStr,
    scheduledEndTime: endTimeStr
  };
});

// 获取开始时间的最大值（不能晚于结束时间）
const getMaxStartTime = () => {
  if (form.value.endTime) {
    return form.value.endTime;
  }
  return '24:00';
};

// 获取结束时间的最小值（不能早于开始时间）
const getMinEndTime = () => {
  if (form.value.startTime) {
    return form.value.startTime;
  }
  return '06:00';
};

// 将时间字符串转换为分钟数
const timeToMinutes = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
};

// 将分钟数转换为时间字符串
const minutesToTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

// 开始时间变化处理
const onStartTimeChange = (newStartTime) => {
  if (!newStartTime) return;

  // 如果有结束时间，检查是否需要调整
  if (form.value.endTime) {
    const startMinutes = timeToMinutes(newStartTime);
    const endMinutes = timeToMinutes(form.value.endTime);

    // 如果开始时间晚于或等于结束时间，设置默认时长为60分钟
    if (startMinutes >= endMinutes) {
      const newEndMinutes = startMinutes + 60;
      form.value.endTime = minutesToTime(newEndMinutes);
    }
  } else {
    // 如果没有结束时间，设置默认时长为60分钟
    const startMinutes = timeToMinutes(newStartTime);
    const newEndMinutes = startMinutes + 60;
    form.value.endTime = minutesToTime(newEndMinutes);
  }
};

// 结束时间变化处理
const onEndTimeChange = (newEndTime) => {
  if (!newEndTime) return;

  // 如果有开始时间，检查是否需要调整
  if (form.value.startTime) {
    const startMinutes = timeToMinutes(form.value.startTime);
    const endMinutes = timeToMinutes(newEndTime);

    // 如果结束时间早于或等于开始时间，设置默认时长为60分钟
    if (endMinutes <= startMinutes) {
      const newStartMinutes = Math.max(endMinutes - 60, timeToMinutes('06:00'));
      form.value.startTime = minutesToTime(newStartMinutes);
    }
  } else {
    // 如果没有开始时间，设置默认时长为60分钟
    const endMinutes = timeToMinutes(newEndTime);
    const newStartMinutes = Math.max(endMinutes - 60, timeToMinutes('06:00'));
    form.value.startTime = minutesToTime(newStartMinutes);
  }
};

// 初始化表单数据
const initializeForm = () => {
  console.log('开始初始化表单数据, props.course:', props.course);

  if (!props.course || !props.course.id) {
    console.warn('课程数据不存在，无法初始化表单');
    return;
  }

  // 使用计算属性中的课程信息
  const info = courseInfo.value;
  console.log('计算属性courseInfo:', info);

  // 从课程信息中提取时间
  let startTime = '';
  let endTime = '';

  if (info.scheduledStartTime) {
    try {
      const startDate = new Date(info.scheduledStartTime);
      if (!isNaN(startDate.getTime())) {
        startTime = startDate.toTimeString().slice(0, 5);
        console.log('解析开始时间成功:', startTime);
      }
    } catch (error) {
      console.error('解析开始时间失败:', error);
    }
  }

  if (info.scheduledEndTime) {
    try {
      const endDate = new Date(info.scheduledEndTime);
      if (!isNaN(endDate.getTime())) {
        endTime = endDate.toTimeString().slice(0, 5);
        console.log('解析结束时间成功:', endTime);
      }
    } catch (error) {
      console.error('解析结束时间失败:', error);
    }
  }

  const formData = {
    courseDate: info.courseDate || '',
    startTime: startTime,
    endTime: endTime,
    recordingUrl: "",
    images: "",
    description: "",
  };

  console.log('设置表单数据:', formData);
  form.value = formData;

  // 强制触发表单验证清除
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitting.value = true;

    // 构造消课数据
    const consumeData = {
      courseId: props.course.id,
      courseDate: form.value.courseDate,
      startTime: form.value.startTime,
      endTime: form.value.endTime,
      recordingUrl: form.value.recordingUrl,
      images: form.value.images,
      description: form.value.description,
    };

    const success = await curriculumStore.consumeCourse(consumeData);

    if (success) {
      ElMessage.success("消课成功");
      emit("success");
      handleClose();
    }
  } catch (error) {
    console.error("消课失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  // 重置表单
  form.value = {
    courseDate: "",
    startTime: "",
    endTime: "",
    recordingUrl: "",
    images: "",
    description: "",
  };
  formRef.value?.clearValidate();
};

// 监听对话框显示状态
watch(visible, (newValue) => {
  console.log('=== 对话框显示状态变化 ===', newValue);
  console.log('当前props.course:', props.course);

  if (newValue) {
    console.log('对话框打开，开始初始化...');

    // 使用 nextTick 确保 DOM 更新完成后再初始化
    nextTick(() => {
      console.log('nextTick 执行初始化');
      initializeForm();
    });

    // 添加一个小延迟，确保所有数据都已经传递完成
    setTimeout(() => {
      console.log('延迟100ms 执行初始化');
      initializeForm();
    }, 100);
  }
});

// 监听课程变化，确保课程数据更新时重新初始化
watch(() => props.course, (newCourse) => {
  console.log('=== 课程数据变化 ===', newCourse);
  console.log('对话框是否可见:', visible.value);

  if (newCourse && visible.value) {
    console.log('课程数据变化且对话框可见，执行初始化');
    nextTick(() => {
      initializeForm();
    });
  }
}, { deep: true });

// 组件挂载时的调试信息
onMounted(() => {
  console.log('=== ConsumeCourseDialog 组件已挂载 ===');
  console.log('挂载时的props.course:', props.course);
  console.log('挂载时的visible:', visible.value);

  // 如果挂载时对话框就是可见的，立即初始化表单
  if (visible.value && props.course) {
    console.log('挂载时对话框可见，立即初始化表单');
    nextTick(() => {
      initializeForm();
    });
  }
});
</script>

<style lang="scss" scoped>
.consume-course-dialog {
  .course-info-card {
    margin-bottom: 20px;

    .course-info-title {
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-descriptions__label) {
      font-weight: 500;
      color: #606266;
    }

    :deep(.el-descriptions__content) {
      color: #303133;
    }
  }

  .consume-course-form {
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    .upload-instructions {
      margin-top: 8px;

      .upload-tip {
        font-size: 12px;
        margin-bottom: 8px;
      }

      .upload-description {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 12px;
        font-size: 13px;
        line-height: 1.5;

        p {
          margin: 0 0 8px 0;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            color: #303133;
            font-weight: 600;
          }
        }

        p:first-child {
          color: #606266;
          font-weight: 600;
          margin-bottom: 10px;
        }

        p:not(:first-child) {
          color: #666;
          padding-left: 8px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 3px;
            height: 3px;
            background-color: #909399;
            border-radius: 50%;
          }
        }
      }
    }
  }
}
</style>
